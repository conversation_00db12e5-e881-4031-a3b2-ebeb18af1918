"""
Configuration management for AI Terminal.

Handles application settings, user preferences, API keys, and
persistent configuration storage with encryption support.
"""

import json
import os
from pathlib import Path
from typing import Any, Dict, Optional, Union

import keyring
import toml
from cryptography.fernet import Fernet
from pydantic import BaseModel, Field


class AIProviderConfig(BaseModel):
    """Configuration for AI providers."""
    
    name: str
    api_key_service: str
    api_key_username: str
    base_url: Optional[str] = None
    model: str
    max_tokens: int = 4096
    temperature: float = 0.7
    enabled: bool = True


class SecurityConfig(BaseModel):
    """Security configuration."""
    
    encrypt_storage: bool = True
    require_approval: bool = True
    dangerous_commands: list[str] = Field(default_factory=lambda: [
        "rm", "del", "format", "fdisk", "dd", "mkfs", "shutdown", "reboot"
    ])
    allowed_file_extensions: list[str] = Field(default_factory=lambda: [
        ".py", ".js", ".ts", ".html", ".css", ".json", ".yaml", ".yml", 
        ".md", ".txt", ".sh", ".bat", ".ps1", ".sql", ".toml", ".ini"
    ])


class UIConfig(BaseModel):
    """UI configuration."""
    
    theme: str = "dark"
    show_timestamps: bool = True
    auto_scroll: bool = True
    syntax_highlighting: bool = True
    show_token_count: bool = True
    animation_speed: float = 1.0
    max_history_display: int = 1000


class Config:
    """Main configuration manager for AI Terminal."""
    
    def __init__(self, config_dir: Optional[Path] = None):
        """Initialize configuration manager."""
        self.config_dir = config_dir or self._get_default_config_dir()
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        self.config_file = self.config_dir / "config.toml"
        self.session_dir = self.config_dir / "sessions"
        self.session_dir.mkdir(exist_ok=True)
        
        self._config: Dict[str, Any] = {}
        self._encryption_key: Optional[bytes] = None
        
        self.load()
    
    def _get_default_config_dir(self) -> Path:
        """Get the default configuration directory."""
        if os.name == "nt":  # Windows
            base = Path(os.environ.get("APPDATA", "~"))
        else:  # Unix-like
            base = Path(os.environ.get("XDG_CONFIG_HOME", "~/.config"))
        
        return (base / "ai-terminal").expanduser()
    
    def _get_encryption_key(self) -> bytes:
        """Get or create encryption key."""
        if self._encryption_key is None:
            try:
                key_str = keyring.get_password("ai-terminal", "encryption-key")
                if key_str:
                    self._encryption_key = key_str.encode()
                else:
                    self._encryption_key = Fernet.generate_key()
                    keyring.set_password(
                        "ai-terminal", "encryption-key", 
                        self._encryption_key.decode()
                    )
            except Exception:
                # Fallback to file-based key storage
                key_file = self.config_dir / ".key"
                if key_file.exists():
                    self._encryption_key = key_file.read_bytes()
                else:
                    self._encryption_key = Fernet.generate_key()
                    key_file.write_bytes(self._encryption_key)
                    key_file.chmod(0o600)  # Restrict permissions
        
        return self._encryption_key
    
    def load(self) -> None:
        """Load configuration from file."""
        if self.config_file.exists():
            try:
                self._config = toml.load(self.config_file)
            except Exception as e:
                print(f"Warning: Failed to load config: {e}")
                self._config = {}
        else:
            self._config = self._get_default_config()
    
    def save(self) -> None:
        """Save configuration to file."""
        try:
            with open(self.config_file, "w") as f:
                toml.dump(self._config, f)
        except Exception as e:
            print(f"Warning: Failed to save config: {e}")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration."""
        return {
            "ai": {
                "default_provider": "deepseek",
                "default_model": "deepseek-chat",
                "providers": {}
            },
            "security": {
                "encrypt_storage": True,
                "require_approval": True,
                "dangerous_commands": [
                    "rm", "del", "format", "fdisk", "dd", "mkfs", 
                    "shutdown", "reboot", "sudo rm", "sudo del"
                ]
            },
            "ui": {
                "theme": "dark",
                "show_timestamps": True,
                "auto_scroll": True,
                "syntax_highlighting": True,
                "show_token_count": True,
                "animation_speed": 1.0
            },
            "session": {
                "auto_save": True,
                "max_history": 10000,
                "default_session": "main"
            },
            "tools": {
                "enabled": [
                    "shell", "file_ops", "git", "code_assist", 
                    "package_manager", "system_monitor"
                ],
                "shell_timeout": 30,
                "max_file_size": 10485760  # 10MB
            }
        }
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value using dot notation."""
        keys = key.split(".")
        value = self._config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any) -> None:
        """Set configuration value using dot notation."""
        keys = key.split(".")
        config = self._config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
        self.save()
    
    def is_configured(self) -> bool:
        """Check if the application is properly configured."""
        providers = self.get("ai.providers", {})
        return len(providers) > 0 and any(
            provider.get("api_key_service") and provider.get("api_key_username")
            for provider in providers.values()
        )
    
    def get_api_key(self, provider: str) -> Optional[str]:
        """Get API key for a provider from secure storage."""
        provider_config = self.get(f"ai.providers.{provider}")
        if not provider_config:
            return None
        
        service = provider_config.get("api_key_service")
        username = provider_config.get("api_key_username")
        
        if not service or not username:
            return None
        
        try:
            return keyring.get_password(service, username)
        except Exception:
            return None
    
    def set_api_key(self, provider: str, api_key: str) -> None:
        """Set API key for a provider in secure storage."""
        service = f"ai-terminal-{provider}"
        username = "api-key"
        
        try:
            keyring.set_password(service, username, api_key)
            self.set(f"ai.providers.{provider}.api_key_service", service)
            self.set(f"ai.providers.{provider}.api_key_username", username)
        except Exception as e:
            print(f"Warning: Failed to store API key securely: {e}")
    
    def get_provider_config(self, provider: str) -> Optional[AIProviderConfig]:
        """Get provider configuration."""
        config_data = self.get(f"ai.providers.{provider}")
        if not config_data:
            return None
        
        try:
            return AIProviderConfig(**config_data)
        except Exception:
            return None
    
    def add_provider(self, provider_config: AIProviderConfig) -> None:
        """Add a new provider configuration."""
        self.set(f"ai.providers.{provider_config.name}", provider_config.dict())
    
    def get_security_config(self) -> SecurityConfig:
        """Get security configuration."""
        return SecurityConfig(**self.get("security", {}))
    
    def get_ui_config(self) -> UIConfig:
        """Get UI configuration."""
        return UIConfig(**self.get("ui", {}))
