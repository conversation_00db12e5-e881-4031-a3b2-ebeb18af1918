#!/bin/bash
# =============================================================================
# AI Terminal - Fully Automatic WSL Installation Script
# =============================================================================
# This script automatically installs and configures AI Terminal in WSL
# Compatible with: Ubuntu 20.04+, Debian 11+, and other Debian-based WSL distributions
# Optimized for: Ubuntu 24.04 LTS (Noble Numbat) with Python 3.12
#
# Features:
#   • Smart detection of existing installations with incremental updates
#   • Support for Python 3.9-3.12 with automatic version selection
#   • Modern WSL2 features including systemd and GPU support detection
#   • Comprehensive security validation and package verification
#   • Enterprise-grade error handling and logging
#   • Cross-platform compatibility and backward compatibility
#
# Usage:
#   1. Copy this script to your WSL environment
#   2. Make it executable: chmod +x auto_install_wsl.sh
#   3. Run it: ./auto_install_wsl.sh
#
# Advanced Usage:
#   ./auto_install_wsl.sh --help          # Show all options
#   ./auto_install_wsl.sh --check         # Check installation status
#   ./auto_install_wsl.sh --force         # Force complete reinstall
#   ./auto_install_wsl.sh --update        # Update components only
# =============================================================================

set -e  # Exit on any error
set -u  # Exit on undefined variables
set -o pipefail  # Exit on pipe failures

# Colors for output (with fallback for non-color terminals)
if [[ -t 1 ]] && command -v tput >/dev/null 2>&1 && tput colors >/dev/null 2>&1; then
    RED='\033[0;31m'
    GREEN='\033[0;32m'
    YELLOW='\033[1;33m'
    BLUE='\033[0;34m'
    PURPLE='\033[0;35m'
    CYAN='\033[0;36m'
    BOLD='\033[1m'
    DIM='\033[2m'
    NC='\033[0m' # No Color
else
    RED=''
    GREEN=''
    YELLOW=''
    BLUE=''
    PURPLE=''
    CYAN=''
    BOLD=''
    DIM=''
    NC=''
fi

# Configuration
PROJECT_NAME="ai-terminal"
INSTALL_DIR="$HOME/$PROJECT_NAME"
GLOBAL_INSTALL_DIR="/usr/local/bin"
WINDOWS_PROJECT_PATH="/mnt/c/Users/<USER>/OneDrive/Documents/New folder"
PREFERRED_PYTHON_VERSION="3.12"  # Ubuntu 24.04 default
FALLBACK_PYTHON_VERSION="3.11"   # Fallback for older systems
CONFIG_FILE="$HOME/.ai-terminal-install-state"

# Command line flags (initialize to prevent unbound variable errors)
FORCE_REINSTALL=false
UPDATE_ONLY=false
SETUP_ONLY=false
DEBUG=false

# Version tracking
SCRIPT_VERSION="3.0"
MIN_PYTHON_VERSION="3.9"
SUPPORTED_PYTHON_VERSIONS=("3.9" "3.10" "3.11" "3.12")

# Feature flags
INSTALL_GLOBALLY=true
ENABLE_SYSTEMD_DETECTION=true
ENABLE_GPU_DETECTION=true
ENABLE_SECURITY_VALIDATION=true
VERIFY_PACKAGE_SIGNATURES=true

# =============================================================================
# Utility Functions
# =============================================================================

print_header() {
    clear
    echo -e "${PURPLE}${BOLD}"
    echo "╔══════════════════════════════════════════════════════════════════════════════╗"
    echo "║                     🚀 AI Terminal WSL Auto-Installer v${SCRIPT_VERSION}                    ║"
    echo "║                                                                              ║"
    echo -e "║  This script will automatically install and configure AI Terminal in WSL    ║"
    echo -e "║  ${GREEN}• Install Python 3.9-3.12 with automatic version detection${PURPLE}             ║"
    echo -e "║  ${GREEN}• Copy project files from Windows with integrity verification${PURPLE}          ║"
    echo -e "║  ${GREEN}• Install all dependencies with security validation${PURPLE}                   ║"
    echo -e "║  ${GREEN}• Configure modern WSL2 features (systemd, GPU support)${PURPLE}               ║"
    echo -e "║  ${GREEN}• Run interactive onboarding wizard${PURPLE}                                   ║"
    echo -e "║  ${GREEN}• Create convenient global commands and shortcuts${PURPLE}                     ║"
    echo "╚══════════════════════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    echo ""
}

print_step() {
    local step_num="${2:-}"
    local total_steps="${3:-}"
    local progress=""

    if [[ -n "$step_num" && -n "$total_steps" ]]; then
        progress=" (${step_num}/${total_steps})"
    fi

    echo -e "${CYAN}${BOLD}[STEP${progress}]${NC} ${CYAN}$1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}" >&2
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_debug() {
    if [[ "${DEBUG:-false}" == "true" ]]; then
        echo -e "${DIM}🔍 DEBUG: $1${NC}" >&2
    fi
}

# Enhanced command checking with version support
check_command() {
    local cmd="$1"
    local min_version="${2:-}"

    if ! command -v "$cmd" &> /dev/null; then
        return 1
    fi

    # If version checking is requested
    if [[ -n "$min_version" ]]; then
        local current_version
        case "$cmd" in
            python*|pip*)
                current_version=$($cmd --version 2>&1 | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' | head -1)
                ;;
            poetry)
                current_version=$($cmd --version 2>&1 | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' | head -1)
                ;;
            *)
                return 0  # Skip version check for unknown commands
                ;;
        esac

        if [[ -n "$current_version" ]]; then
            if version_compare "$current_version" "$min_version"; then
                return 0
            else
                return 2  # Command exists but version is too old
            fi
        fi
    fi

    return 0
}

# Version comparison function (returns 0 if version1 >= version2)
version_compare() {
    local version1="$1"
    local version2="$2"

    # Convert versions to comparable format
    local v1_major v1_minor v1_patch
    local v2_major v2_minor v2_patch

    IFS='.' read -r v1_major v1_minor v1_patch <<< "$version1"
    IFS='.' read -r v2_major v2_minor v2_patch <<< "$version2"

    # Default patch versions to 0 if not specified
    v1_patch="${v1_patch:-0}"
    v2_patch="${v2_patch:-0}"

    # Compare major.minor.patch
    if (( v1_major > v2_major )); then
        return 0
    elif (( v1_major < v2_major )); then
        return 1
    elif (( v1_minor > v2_minor )); then
        return 0
    elif (( v1_minor < v2_minor )); then
        return 1
    elif (( v1_patch >= v2_patch )); then
        return 0
    else
        return 1
    fi
}

# Enhanced logging with timestamps and levels
log_message() {
    local level="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')

    case "$level" in
        ERROR)
            echo -e "${timestamp} ${RED}[ERROR]${NC} $message" >&2
            ;;
        WARN)
            echo -e "${timestamp} ${YELLOW}[WARN]${NC} $message"
            ;;
        INFO)
            echo -e "${timestamp} ${BLUE}[INFO]${NC} $message"
            ;;
        DEBUG)
            if [[ "${DEBUG:-false}" == "true" ]]; then
                echo -e "${timestamp} ${DIM}[DEBUG]${NC} $message" >&2
            fi
            ;;
    esac
}

# System information detection
detect_system_info() {
    local info_file="/tmp/ai-terminal-system-info"

    {
        echo "TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')"
        echo "HOSTNAME=$(hostname)"
        echo "USER=$(whoami)"
        echo "HOME=$HOME"
        echo "PWD=$PWD"

        # WSL information
        if [[ -f /proc/version ]]; then
            echo "WSL_VERSION=$(grep -oE 'WSL[0-9]*' /proc/version || echo 'WSL1')"
            echo "KERNEL_VERSION=$(uname -r)"
        fi

        # Distribution information
        if [[ -f /etc/os-release ]]; then
            source /etc/os-release
            echo "DISTRO_ID=$ID"
            echo "DISTRO_VERSION=$VERSION_ID"
            echo "DISTRO_NAME=\"$PRETTY_NAME\""
        fi

        # System resources
        echo "MEMORY_TOTAL=$(free -h | awk '/^Mem:/ {print $2}')"
        echo "DISK_AVAILABLE=$(df -h "$HOME" | awk 'NR==2 {print $4}')"
        echo "CPU_CORES=$(nproc)"

        # Python versions available
        for version in "${SUPPORTED_PYTHON_VERSIONS[@]}"; do
            if command -v "python$version" >/dev/null 2>&1; then
                local py_version=$(python$version --version 2>&1 | grep -oE '[0-9]+\.[0-9]+\.[0-9]+')
                echo "PYTHON_${version//./_}_VERSION=$py_version"
                echo "PYTHON_${version//./_}_PATH=$(which python$version)"
            fi
        done

        # Systemd detection
        if [[ "$ENABLE_SYSTEMD_DETECTION" == "true" ]]; then
            if systemctl --version >/dev/null 2>&1; then
                echo "SYSTEMD_AVAILABLE=true"
                echo "SYSTEMD_VERSION=$(systemctl --version | head -1 | grep -oE '[0-9]+' | head -1)"
            else
                echo "SYSTEMD_AVAILABLE=false"
            fi
        fi

        # GPU detection
        if [[ "$ENABLE_GPU_DETECTION" == "true" ]]; then
            if command -v nvidia-smi >/dev/null 2>&1; then
                echo "GPU_NVIDIA=true"
                echo "GPU_NVIDIA_VERSION=$(nvidia-smi --query-gpu=driver_version --format=csv,noheader,nounits | head -1)"
            else
                echo "GPU_NVIDIA=false"
            fi
        fi

    } > "$info_file"

    # Source the info file for use in the script
    source "$info_file"
    print_debug "System information detected and cached to $info_file"
}

# =============================================================================
# State Management Functions
# =============================================================================

save_install_state() {
    local component="$1"
    local version="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')

    # Create or update state file
    touch "$CONFIG_FILE"

    # Remove old entry for this component
    grep -v "^$component=" "$CONFIG_FILE" > "${CONFIG_FILE}.tmp" 2>/dev/null || true
    mv "${CONFIG_FILE}.tmp" "$CONFIG_FILE" 2>/dev/null || true

    # Add new entry
    echo "$component=$version|$timestamp" >> "$CONFIG_FILE"
}

get_install_state() {
    local component="$1"

    if [[ -f "$CONFIG_FILE" ]]; then
        grep "^$component=" "$CONFIG_FILE" | cut -d'=' -f2 | cut -d'|' -f1
    fi
}

is_component_installed() {
    local component="$1"
    local required_version="$2"
    local installed_version=$(get_install_state "$component")

    if [[ -n "$installed_version" ]]; then
        if [[ -z "$required_version" ]] || [[ "$installed_version" == "$required_version" ]]; then
            return 0
        fi
    fi
    return 1
}

# Enhanced Python version detection with support for multiple versions
detect_best_python() {
    local best_python=""
    local best_version=""
    local best_score=0

    print_debug "Detecting available Python versions..."

    # Check for preferred version first (Python 3.12 for Ubuntu 24.04)
    if check_command "python$PREFERRED_PYTHON_VERSION"; then
        local version=$(python$PREFERRED_PYTHON_VERSION --version 2>&1 | grep -oE '[0-9]+\.[0-9]+\.[0-9]+')
        if [[ -n "$version" ]] && version_compare "$version" "$MIN_PYTHON_VERSION"; then
            echo "python$PREFERRED_PYTHON_VERSION"
            print_debug "Found preferred Python $PREFERRED_PYTHON_VERSION: $version"
            return 0
        fi
    fi

    # Check system python3 (common on Ubuntu 24.04)
    if check_command "python3"; then
        local version=$(python3 --version 2>&1 | grep -oE '[0-9]+\.[0-9]+\.[0-9]+')
        if [[ -n "$version" ]] && version_compare "$version" "$MIN_PYTHON_VERSION"; then
            echo "python3"
            print_debug "Found system Python3: $version"
            return 0
        fi
    fi

    # Check all supported versions in reverse order (newest first)
    for ((i=${#SUPPORTED_PYTHON_VERSIONS[@]}-1; i>=0; i--)); do
        local py_version="${SUPPORTED_PYTHON_VERSIONS[i]}"
        local py_cmd="python$py_version"

        if check_command "$py_cmd"; then
            local version=$($py_cmd --version 2>&1 | grep -oE '[0-9]+\.[0-9]+\.[0-9]+')
            if [[ -n "$version" ]] && version_compare "$version" "$MIN_PYTHON_VERSION"; then
                echo "$py_cmd"
                print_debug "Found Python $py_version: $version"
                return 0
            fi
        fi
    done

    # Check fallback version
    if [[ "$FALLBACK_PYTHON_VERSION" != "$PREFERRED_PYTHON_VERSION" ]]; then
        if check_command "python$FALLBACK_PYTHON_VERSION"; then
            local version=$(python$FALLBACK_PYTHON_VERSION --version 2>&1 | grep -oE '[0-9]+\.[0-9]+\.[0-9]+')
            if [[ -n "$version" ]] && version_compare "$version" "$MIN_PYTHON_VERSION"; then
                echo "python$FALLBACK_PYTHON_VERSION"
                print_debug "Found fallback Python $FALLBACK_PYTHON_VERSION: $version"
                return 0
            fi
        fi
    fi

    print_debug "No suitable Python version found"
    return 1
}

# Legacy function for backward compatibility
check_python_version() {
    local python_cmd=$(detect_best_python)
    if [[ -n "$python_cmd" ]]; then
        local version=$($python_cmd --version 2>&1 | grep -oE '[0-9]+\.[0-9]+\.[0-9]+')
        echo "$version"
        return 0
    fi
    return 1
}

# Get the Python command to use for installation
get_python_command() {
    local python_cmd=$(detect_best_python)
    if [[ -n "$python_cmd" ]]; then
        echo "$python_cmd"
        return 0
    fi

    # Fallback to python3 if available
    if check_command "python3"; then
        echo "python3"
        return 0
    fi

    return 1
}

check_project_needs_update() {
    if [[ ! -d "$INSTALL_DIR" ]]; then
        return 0  # Needs installation
    fi

    # Check if Windows project is newer
    if [[ -d "$WINDOWS_PROJECT_PATH" ]]; then
        local windows_time=$(stat -c %Y "$WINDOWS_PROJECT_PATH" 2>/dev/null || echo 0)
        local local_time=$(stat -c %Y "$INSTALL_DIR" 2>/dev/null || echo 0)

        if [[ $windows_time -gt $local_time ]]; then
            return 0  # Needs update
        fi
    fi

    return 1  # No update needed
}

detect_existing_installation() {
    print_step "Detecting existing installation..."

    local needs_update=false

    # Check system packages
    if ! is_component_installed "system_packages" "$(date +%Y-%m-%d)"; then
        print_info "System packages need updating"
        needs_update=true
    else
        print_success "System packages are up to date"
    fi

    # Check Python (check for compatible version, not just specific version)
    if ! check_python_version >/dev/null 2>&1; then
        # Check if we have system Python 3.12+ on Ubuntu 24.04
        local ubuntu_version=$(lsb_release -rs 2>/dev/null || echo "unknown")
        if [[ "$ubuntu_version" == "24.04" ]] && python3 --version >/dev/null 2>&1; then
            local py_version=$(python3 --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+')
            local major=$(echo "$py_version" | cut -d. -f1)
            local minor=$(echo "$py_version" | cut -d. -f2)
            if [[ "$major" -eq 3 && "$minor" -ge 11 ]]; then
                print_success "Python $py_version is installed (system)"
                save_install_state "python" "$py_version"
            else
                print_info "Python $PYTHON_VERSION needs installation"
                needs_update=true
            fi
        else
            print_info "Python $PYTHON_VERSION needs installation"
            needs_update=true
        fi
    else
        local py_version=$(check_python_version)
        print_success "Python $py_version is installed"
        save_install_state "python" "$py_version"
    fi

    # Check Poetry
    if ! check_command "poetry"; then
        print_info "Poetry needs installation"
        needs_update=true
    else
        local poetry_version=$(poetry --version 2>/dev/null | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' || echo "unknown")
        print_success "Poetry $poetry_version is installed"
        save_install_state "poetry" "$poetry_version"
    fi

    # Check project
    if check_project_needs_update; then
        print_info "AI Terminal project needs update"
        needs_update=true
    else
        print_success "AI Terminal project is up to date"
    fi

    # Check dependencies
    if [[ -d "$INSTALL_DIR" ]]; then
        cd "$INSTALL_DIR"
        if [[ -f "pyproject.toml" ]] && check_command "poetry"; then
            if ! poetry check >/dev/null 2>&1; then
                print_info "Project dependencies need update"
                needs_update=true
            else
                print_success "Project dependencies are satisfied"
            fi
        fi
    fi

    if [[ "$needs_update" == "false" ]]; then
        print_success "All components are up to date!"
        return 1  # No update needed
    fi

    return 0  # Update needed
}

# =============================================================================
# Installation Functions
# =============================================================================

check_wsl() {
    print_step "Checking WSL environment..."
    
    if [[ ! -f /proc/version ]] || ! grep -q "microsoft\|WSL" /proc/version; then
        print_error "This script must be run in WSL (Windows Subsystem for Linux)"
        exit 1
    fi
    
    print_success "WSL environment detected"
}

check_sudo() {
    print_step "Checking sudo access..."

    # Test sudo access
    if sudo -n true 2>/dev/null; then
        print_success "Sudo access confirmed"
        return 0
    fi

    print_info "This script requires sudo access for system package installation"
    print_info "You may be prompted for your password once"

    # Prompt for sudo password and cache it
    if sudo -v; then
        print_success "Sudo access granted"
        return 0
    else
        print_error "Sudo access required. Please run: sudo -v"
        exit 1
    fi
}

update_system() {
    if is_component_installed "system_packages" "$(date +%Y-%m-%d)"; then
        print_success "System packages already updated today"
        return
    fi

    print_step "Updating system packages..."

    # Update package lists
    if sudo apt update -qq; then
        print_info "Package lists updated"
    else
        print_warning "Package list update had issues, continuing..."
    fi

    # Upgrade packages (non-interactive)
    if sudo DEBIAN_FRONTEND=noninteractive apt upgrade -y -qq; then
        print_success "System packages updated"
    else
        print_warning "Some packages couldn't be upgraded, continuing..."
    fi

    save_install_state "system_packages" "$(date +%Y-%m-%d)"
}

install_dependencies() {
    local deps_version="2.0"  # Updated version for comprehensive dependencies

    if is_component_installed "system_deps" "$deps_version"; then
        print_success "System dependencies already installed"
        return
    fi

    print_step "Installing comprehensive system dependencies..." 1 10

    # Update package signatures and keys first
    if [[ "$VERIFY_PACKAGE_SIGNATURES" == "true" ]]; then
        print_info "Updating package signatures and security keys..."
        sudo apt-key adv --refresh-keys --keyserver keyserver.ubuntu.com >/dev/null 2>&1 || true
    fi

    # Core system packages
    local core_packages=(
        "software-properties-common"
        "apt-transport-https"
        "ca-certificates"
        "gnupg"
        "lsb-release"
        "curl"
        "wget"
        "git"
        "unzip"
        "zip"
        "tree"
        "htop"
        "jq"
        "nano"
        "vim"
    )

    # Development tools and libraries
    local dev_packages=(
        "build-essential"
        "gcc"
        "g++"
        "make"
        "cmake"
        "pkg-config"
        "autoconf"
        "automake"
        "libtool"
    )

    # Python development packages
    local python_packages=(
        "python3-dev"
        "python3-pip"
        "python3-venv"
        "python3-setuptools"
        "python3-wheel"
        "python3-distutils"
        "python3-apt"
    )

    # Cryptography and security libraries
    local crypto_packages=(
        "libssl-dev"
        "libffi-dev"
        "libcrypt-dev"
        "libsasl2-dev"
        "libldap2-dev"
        "libkrb5-dev"
    )

    # Database and storage libraries
    local db_packages=(
        "libsqlite3-dev"
        "sqlite3"
        "libpq-dev"
        "libmysqlclient-dev"
    )

    # Multimedia and graphics libraries (for potential future features)
    local media_packages=(
        "libpng-dev"
        "libjpeg-dev"
        "libfreetype6-dev"
        "libxml2-dev"
        "libxslt1-dev"
    )

    # Networking and compression libraries
    local network_packages=(
        "libcurl4-openssl-dev"
        "libbz2-dev"
        "liblzma-dev"
        "zlib1g-dev"
        "libreadline-dev"
        "libncurses5-dev"
        "libncursesw5-dev"
    )

    # System monitoring and utilities
    local util_packages=(
        "psmisc"
        "procps"
        "lsof"
        "strace"
        "tcpdump"
        "net-tools"
        "dnsutils"
        "iputils-ping"
    )

    # Combine all packages
    local all_packages=(
        "${core_packages[@]}"
        "${dev_packages[@]}"
        "${python_packages[@]}"
        "${crypto_packages[@]}"
        "${db_packages[@]}"
        "${media_packages[@]}"
        "${network_packages[@]}"
        "${util_packages[@]}"
    )

    print_info "Installing ${#all_packages[@]} system packages..."

    # Install packages in batches to handle potential failures gracefully
    local batch_size=10
    local installed_count=0
    local failed_packages=()

    for ((i=0; i<${#all_packages[@]}; i+=batch_size)); do
        local batch=("${all_packages[@]:i:batch_size}")
        local batch_str="${batch[*]}"

        print_debug "Installing batch: ${batch_str}"

        if sudo DEBIAN_FRONTEND=noninteractive apt install -y -qq "${batch[@]}" 2>/dev/null; then
            installed_count=$((installed_count + ${#batch[@]}))
            print_debug "Batch installed successfully (${#batch[@]} packages)"
        else
            # Try installing packages individually to identify failures
            for package in "${batch[@]}"; do
                if sudo DEBIAN_FRONTEND=noninteractive apt install -y -qq "$package" 2>/dev/null; then
                    installed_count=$((installed_count + 1))
                    print_debug "Installed: $package"
                else
                    failed_packages+=("$package")
                    print_debug "Failed: $package"
                fi
            done
        fi
    done

    # Report results
    print_success "Installed $installed_count/${#all_packages[@]} system packages"

    if [[ ${#failed_packages[@]} -gt 0 ]]; then
        print_warning "Failed to install ${#failed_packages[@]} packages: ${failed_packages[*]}"
        print_info "This is usually not critical and the installation can continue"
    fi

    # Install additional packages for specific Ubuntu versions
    local ubuntu_version="${DISTRO_VERSION:-unknown}"
    case "$ubuntu_version" in
        "24.04")
            print_info "Installing Ubuntu 24.04 specific packages..."
            sudo DEBIAN_FRONTEND=noninteractive apt install -y -qq \
                python3.12-dev python3.12-venv python3.12-distutils 2>/dev/null || true
            ;;
        "22.04")
            print_info "Installing Ubuntu 22.04 specific packages..."
            sudo DEBIAN_FRONTEND=noninteractive apt install -y -qq \
                python3.10-dev python3.10-venv python3.10-distutils 2>/dev/null || true
            ;;
        "20.04")
            print_info "Installing Ubuntu 20.04 specific packages..."
            sudo DEBIAN_FRONTEND=noninteractive apt install -y -qq \
                python3.8-dev python3.8-venv python3.8-distutils 2>/dev/null || true
            ;;
    esac

    save_install_state "system_deps" "$deps_version"
    print_success "System dependencies installation completed"
}

install_python() {
    print_step "Installing Python..." 2 10

    # Check if we already have a suitable Python version
    local existing_python=$(detect_best_python)
    if [[ -n "$existing_python" ]]; then
        local py_version=$($existing_python --version 2>&1 | grep -oE '[0-9]+\.[0-9]+\.[0-9]+')
        print_success "Python $py_version already available ($existing_python)"
        save_install_state "python" "$py_version"
        return
    fi

    local ubuntu_version="${DISTRO_VERSION:-unknown}"
    print_info "Detected Ubuntu version: $ubuntu_version"

    # Strategy 1: Install system Python packages for the specific Ubuntu version
    case "$ubuntu_version" in
        "24.04")
            print_info "Installing Python 3.12 (Ubuntu 24.04 default)..."
            local packages=(
                "python3"
                "python3-pip"
                "python3-venv"
                "python3-dev"
                "python3-setuptools"
                "python3-wheel"
                "python3-distutils"
            )
            ;;
        "22.04")
            print_info "Installing Python 3.10 (Ubuntu 22.04 default)..."
            local packages=(
                "python3"
                "python3-pip"
                "python3-venv"
                "python3-dev"
                "python3-setuptools"
                "python3-wheel"
                "python3-distutils"
                "python3.10"
                "python3.10-dev"
                "python3.10-venv"
            )
            ;;
        "20.04")
            print_info "Installing Python 3.8 (Ubuntu 20.04 default)..."
            local packages=(
                "python3"
                "python3-pip"
                "python3-venv"
                "python3-dev"
                "python3-setuptools"
                "python3-wheel"
                "python3-distutils"
                "python3.8"
                "python3.8-dev"
                "python3.8-venv"
            )
            ;;
        *)
            print_info "Installing generic Python 3 packages..."
            local packages=(
                "python3"
                "python3-pip"
                "python3-venv"
                "python3-dev"
                "python3-setuptools"
                "python3-wheel"
            )
            ;;
    esac

    # Install system Python packages
    local installed_system=false
    if sudo DEBIAN_FRONTEND=noninteractive apt install -y -qq "${packages[@]}" 2>/dev/null; then
        print_success "System Python packages installed"
        installed_system=true
    else
        print_warning "Some system Python packages failed to install"
    fi

    # Check if we now have a working Python
    local python_cmd=$(detect_best_python)
    if [[ -n "$python_cmd" ]]; then
        local py_version=$($python_cmd --version 2>&1 | grep -oE '[0-9]+\.[0-9]+\.[0-9]+')
        print_success "Python $py_version is now available ($python_cmd)"
        save_install_state "python" "$py_version"

        # Create convenience symlinks
        if [[ "$python_cmd" != "python3" ]] && [[ "$python_cmd" != "python" ]]; then
            sudo ln -sf "$(which $python_cmd)" /usr/local/bin/python3 2>/dev/null || true
            sudo ln -sf "$(which $python_cmd)" /usr/local/bin/python 2>/dev/null || true
        fi

        return
    fi

    # Strategy 2: Try deadsnakes PPA for additional Python versions
    print_info "System Python installation insufficient, trying deadsnakes PPA..."

    if sudo add-apt-repository -y ppa:deadsnakes/ppa 2>/dev/null; then
        print_info "Deadsnakes PPA added successfully"
        sudo apt update -qq

        # Try to install preferred and fallback versions
        for py_ver in "$PREFERRED_PYTHON_VERSION" "$FALLBACK_PYTHON_VERSION"; do
            print_info "Attempting to install Python $py_ver from PPA..."
            local ppa_packages=(
                "python$py_ver"
                "python$py_ver-dev"
                "python$py_ver-venv"
                "python$py_ver-distutils"
            )

            local ppa_success=true
            for package in "${ppa_packages[@]}"; do
                if ! sudo DEBIAN_FRONTEND=noninteractive apt install -y -qq "$package" 2>/dev/null; then
                    print_debug "Failed to install: $package"
                    ppa_success=false
                fi
            done

            if [[ "$ppa_success" == "true" ]] && command -v "python$py_ver" >/dev/null 2>&1; then
                local py_version=$(python$py_ver --version 2>&1 | grep -oE '[0-9]+\.[0-9]+\.[0-9]+')
                print_success "Python $py_version installed from PPA"
                save_install_state "python" "$py_version"

                # Create symlinks
                sudo ln -sf "/usr/bin/python$py_ver" /usr/local/bin/python3 2>/dev/null || true
                sudo ln -sf "/usr/bin/python$py_ver" /usr/local/bin/python 2>/dev/null || true

                return
            fi
        done
    else
        print_warning "Failed to add deadsnakes PPA"
    fi

    # Strategy 3: Install pip manually if we have any Python 3
    if command -v python3 >/dev/null 2>&1; then
        local py_version=$(python3 --version 2>&1 | grep -oE '[0-9]+\.[0-9]+\.[0-9]+')
        local major=$(echo "$py_version" | cut -d. -f1)
        local minor=$(echo "$py_version" | cut -d. -f2)

        if [[ "$major" -eq 3 && "$minor" -ge 9 ]]; then
            print_info "Found Python $py_version, installing pip manually..."

            # Install pip using get-pip.py
            if curl -sSL https://bootstrap.pypa.io/get-pip.py | python3 - --user 2>/dev/null; then
                print_success "Pip installed manually for Python $py_version"
                save_install_state "python" "$py_version"
                return
            fi
        fi
    fi

    # Strategy 4: Final fallback - check if any Python is available
    local final_python=$(detect_best_python)
    if [[ -n "$final_python" ]]; then
        local py_version=$($final_python --version 2>&1 | grep -oE '[0-9]+\.[0-9]+\.[0-9]+')
        print_warning "Using available Python $py_version ($final_python) - may have limited functionality"
        save_install_state "python" "$py_version"
        return
    fi

    # If we get here, Python installation has failed
    print_error "Failed to install a suitable Python version"
    print_error "Minimum required: Python $MIN_PYTHON_VERSION"
    print_error "Please install Python manually and re-run this script"
    exit 1
}

install_poetry() {
    if check_command "poetry"; then
        local poetry_version=$(poetry --version 2>/dev/null | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' || echo "unknown")
        print_success "Poetry $poetry_version already installed"
        save_install_state "poetry" "$poetry_version"
        return
    fi

    print_step "Installing Poetry package manager..."

    # Install Poetry
    curl -sSL https://install.python-poetry.org | python3 - > /dev/null 2>&1

    # Add Poetry to PATH
    export PATH="$HOME/.local/bin:$PATH"

    # Add to shell profile
    if ! grep -q "/.local/bin" ~/.bashrc; then
        echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.bashrc
    fi

    local poetry_version=$(poetry --version 2>/dev/null | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' || echo "unknown")
    save_install_state "poetry" "$poetry_version"
    print_success "Poetry $poetry_version installed"
}

copy_project() {
    if ! check_project_needs_update; then
        print_success "AI Terminal project is up to date"
        return
    fi

    print_step "Updating AI Terminal project from Windows..."

    # Check if Windows project exists
    if [[ ! -d "$WINDOWS_PROJECT_PATH" ]]; then
        print_error "Windows project not found at: $WINDOWS_PROJECT_PATH"
        print_info "Please ensure the project exists in Windows or update the path in this script"
        exit 1
    fi

    # Backup existing configuration if it exists
    local backup_config=""
    if [[ -f "$INSTALL_DIR/.ai-terminal/config.yaml" ]]; then
        backup_config=$(mktemp)
        cp "$INSTALL_DIR/.ai-terminal/config.yaml" "$backup_config"
        print_info "Backing up existing configuration"
    fi

    # Remove existing installation if it exists
    if [[ -d "$INSTALL_DIR" ]]; then
        print_warning "Removing existing installation..."
        rm -rf "$INSTALL_DIR"
    fi

    # Copy project files
    cp -r "$WINDOWS_PROJECT_PATH" "$INSTALL_DIR"

    # Restore configuration if it was backed up
    if [[ -n "$backup_config" && -f "$backup_config" ]]; then
        mkdir -p "$INSTALL_DIR/.ai-terminal"
        cp "$backup_config" "$INSTALL_DIR/.ai-terminal/config.yaml"
        rm "$backup_config"
        print_info "Restored existing configuration"
    fi

    # Set proper permissions
    chmod -R 755 "$INSTALL_DIR"
    chmod +x "$INSTALL_DIR/cli.py" 2>/dev/null || true

    save_install_state "project" "$(date '+%Y-%m-%d %H:%M:%S')"
    print_success "Project updated to $INSTALL_DIR"
}

install_project_dependencies() {
    print_step "Installing project dependencies..."

    cd "$INSTALL_DIR"

    # Determine which Python to use
    local python_cmd="python3"
    if check_command "python$PYTHON_VERSION"; then
        python_cmd="python$PYTHON_VERSION"
    fi

    print_info "Using Python: $python_cmd"

    # Check if pyproject.toml exists (Poetry project)
    if [[ -f "pyproject.toml" ]]; then
        print_info "Using Poetry for dependency management..."
        if poetry install; then
            print_success "Dependencies installed with Poetry"
        else
            print_warning "Poetry installation failed, trying pip fallback..."
            # Fallback to pip installation
            $python_cmd -m venv venv
            source venv/bin/activate
            pip install --upgrade pip
            pip install rich typer pydantic aiohttp asyncio
            print_success "Basic dependencies installed with pip"
        fi
    elif [[ -f "requirements.txt" ]]; then
        print_info "Using pip for dependency management..."
        $python_cmd -m venv venv
        source venv/bin/activate
        pip install --upgrade pip
        if pip install -r requirements.txt; then
            print_success "Dependencies installed with pip"
        else
            print_warning "Some dependencies failed to install, continuing..."
        fi
    else
        print_warning "No dependency file found, installing common packages..."
        $python_cmd -m venv venv
        source venv/bin/activate
        pip install --upgrade pip
        if pip install rich typer pydantic aiohttp; then
            print_success "Basic dependencies installed"
        else
            print_warning "Some basic dependencies failed to install, continuing..."
        fi
    fi
}

create_shortcuts() {
    print_step "Creating convenient shortcuts..." 8 10

    # Determine the best Python command to use
    local python_cmd=$(get_python_command)
    if [[ -z "$python_cmd" ]]; then
        python_cmd="python3"  # Fallback
    fi

    print_info "Using Python command: $python_cmd"

    # Create activation script
    cat > "$INSTALL_DIR/activate.sh" << EOF
#!/bin/bash
# AI Terminal Environment Activation Script
# Generated by auto_install_wsl.sh v${SCRIPT_VERSION}

cd ~/ai-terminal

if [[ -f "pyproject.toml" ]] && command -v poetry &> /dev/null; then
    echo "🚀 Activating Poetry environment..."
    poetry shell
elif [[ -d "venv" ]]; then
    echo "🐍 Activating virtual environment..."
    source venv/bin/activate
else
    echo "⚠️  No virtual environment found"
fi

echo "✅ AI Terminal environment ready!"
echo ""
echo "🎯 Available commands:"
echo "  ai-terminal                 - Start AI Terminal application"
echo "  ait                         - Start AI Terminal application (short)"
echo "  $python_cmd -m ai_terminal  - Start using Python module"
echo ""
echo "🔧 Global commands (available anywhere):"
echo "  ai-chat                     - Start interactive chat"
echo "  ai-quick 'message'          - Send quick message"
echo "  ai-health                   - Check system health"
echo "  ai-config                   - Show configuration"
echo "  ai-setup                    - Run onboarding wizard"
echo ""

exec bash
EOF

    chmod +x "$INSTALL_DIR/activate.sh"

    # Create global wrapper scripts
    if [[ "$INSTALL_GLOBALLY" == "true" ]]; then
        print_info "Installing global commands..."

        # Create global ait command (environment activator)
        sudo tee "$GLOBAL_INSTALL_DIR/ait" > /dev/null << EOF
#!/bin/bash
# AI Terminal Environment Activator
# Generated by auto_install_wsl.sh v${SCRIPT_VERSION}
cd ~/ai-terminal && bash activate.sh
EOF

        # Create global ai-terminal command (main application)
        sudo tee "$GLOBAL_INSTALL_DIR/ai-terminal" > /dev/null << EOF
#!/bin/bash
# AI Terminal Main Application
# Generated by auto_install_wsl.sh v${SCRIPT_VERSION}
cd ~/ai-terminal
if [[ -f "pyproject.toml" ]] && command -v poetry &> /dev/null; then
    poetry run ai-terminal "\$@"
elif [[ -d "venv" ]]; then
    source venv/bin/activate && ai-terminal "\$@"
elif command -v ai-terminal &> /dev/null; then
    ai-terminal "\$@"
else
    $python_cmd -m ai_terminal "\$@"
fi
EOF

        # Create global ai-chat command (interactive chat)
        sudo tee "$GLOBAL_INSTALL_DIR/ai-chat" > /dev/null << EOF
#!/bin/bash
# AI Terminal Chat Interface
# Generated by auto_install_wsl.sh v${SCRIPT_VERSION}
cd ~/ai-terminal
if [[ -f "pyproject.toml" ]] && command -v poetry &> /dev/null; then
    poetry run ai-terminal "\$@"
elif [[ -d "venv" ]]; then
    source venv/bin/activate && ai-terminal "\$@"
elif command -v ai-terminal &> /dev/null; then
    ai-terminal "\$@"
else
    $python_cmd -m ai_terminal "\$@"
fi
EOF

        # Create global ai-quick command (quick messages)
        sudo tee "$GLOBAL_INSTALL_DIR/ai-quick" > /dev/null << EOF
#!/bin/bash
# AI Terminal Quick Message
# Generated by auto_install_wsl.sh v${SCRIPT_VERSION}
if [[ \$# -eq 0 ]]; then
    echo "Usage: ai-quick 'your message here'"
    echo "Example: ai-quick 'What is the weather like?'"
    exit 1
fi
cd ~/ai-terminal
if [[ -f "pyproject.toml" ]] && command -v poetry &> /dev/null; then
    poetry run ai-terminal --quick "\$*"
elif [[ -d "venv" ]]; then
    source venv/bin/activate && ai-terminal --quick "\$*"
elif command -v ai-terminal &> /dev/null; then
    ai-terminal --quick "\$*"
else
    $python_cmd -m ai_terminal --quick "\$*"
fi
EOF

        # Create global ai-health command (system health check)
        sudo tee "$GLOBAL_INSTALL_DIR/ai-health" > /dev/null << EOF
#!/bin/bash
# AI Terminal Health Check
# Generated by auto_install_wsl.sh v${SCRIPT_VERSION}
cd ~/ai-terminal
if [[ -f "pyproject.toml" ]] && command -v poetry &> /dev/null; then
    poetry run ai-terminal --health "\$@"
elif [[ -d "venv" ]]; then
    source venv/bin/activate && ai-terminal --health "\$@"
elif command -v ai-terminal &> /dev/null; then
    ai-terminal --health "\$@"
else
    $python_cmd -m ai_terminal --health "\$@"
fi
EOF

        # Create global ai-config command (configuration)
        sudo tee "$GLOBAL_INSTALL_DIR/ai-config" > /dev/null << EOF
#!/bin/bash
# AI Terminal Configuration
# Generated by auto_install_wsl.sh v${SCRIPT_VERSION}
cd ~/ai-terminal
if [[ -f "pyproject.toml" ]] && command -v poetry &> /dev/null; then
    poetry run ai-terminal --config "\$@"
elif [[ -d "venv" ]]; then
    source venv/bin/activate && ai-terminal --config "\$@"
elif command -v ai-terminal &> /dev/null; then
    ai-terminal --config "\$@"
else
    $python_cmd -m ai_terminal --config "\$@"
fi
EOF

        # Create global ai-setup command (onboarding wizard)
        sudo tee "$GLOBAL_INSTALL_DIR/ai-setup" > /dev/null << EOF
#!/bin/bash
# AI Terminal Setup Wizard
# Generated by auto_install_wsl.sh v${SCRIPT_VERSION}
cd ~/ai-terminal
if [[ -f "pyproject.toml" ]] && command -v poetry &> /dev/null; then
    poetry run ai-terminal --no-onboarding "\$@"
elif [[ -d "venv" ]]; then
    source venv/bin/activate && ai-terminal --no-onboarding "\$@"
elif command -v ai-terminal &> /dev/null; then
    ai-terminal --no-onboarding "\$@"
else
    $python_cmd -m ai_terminal --no-onboarding "\$@"
fi
EOF

        # Make all global commands executable
        local global_commands=("ait" "ai-terminal" "ai-chat" "ai-quick" "ai-health" "ai-config" "ai-setup")
        for cmd in "\${global_commands[@]}"; do
            sudo chmod +x "$GLOBAL_INSTALL_DIR/\$cmd"
            print_debug "Made executable: $GLOBAL_INSTALL_DIR/\$cmd"
        done

        print_success "Global commands installed to $GLOBAL_INSTALL_DIR"
        print_info "Installed commands: \${global_commands[*]}"
    fi

    # Add aliases to bashrc as backup
    if ! grep -q "ai-terminal aliases" ~/.bashrc; then
        cat >> ~/.bashrc << EOF

# ai-terminal aliases (backup to global commands)
alias ait-local='cd ~/ai-terminal && bash activate.sh'
alias ai-chat-local='cd ~/ai-terminal && python cli.py chat'
alias ai-quick-local='cd ~/ai-terminal && python cli.py quick'
alias ai-health-local='cd ~/ai-terminal && python cli.py health'
alias ai-config-local='cd ~/ai-terminal && python cli.py config --show'
alias ai-setup-local='cd ~/ai-terminal && python cli.py setup'
EOF
    fi

    print_success "Shortcuts and global commands created"
}

test_installation() {
    print_step "Testing installation..." 9 10

    cd "$INSTALL_DIR"

    # Determine which Python to use
    local python_cmd=$(get_python_command)
    if [[ -z "$python_cmd" ]]; then
        python_cmd="python3"  # Fallback
    fi

    print_info "Testing with Python command: $python_cmd"

    # Test 1: Basic Python module import
    print_info "Testing Python module import..."
    if [[ -f "pyproject.toml" ]] && command -v poetry &> /dev/null; then
        if poetry run python -c "import ai_terminal; print('✓ Module import successful')" &> /dev/null; then
            print_success "Module import test passed with Poetry"
        else
            print_warning "Poetry module import failed, trying fallback..."
            if $python_cmd -c "import ai_terminal; print('✓ Module import successful')" &> /dev/null; then
                print_success "Module import test passed with $python_cmd"
            else
                print_warning "Module import test failed - this may be normal if dependencies aren't fully installed"
            fi
        fi
    elif [[ -d "venv" ]]; then
        source venv/bin/activate
        if python -c "import ai_terminal; print('✓ Module import successful')" &> /dev/null; then
            print_success "Module import test passed with venv"
        else
            print_warning "venv module import failed, trying fallback..."
            if $python_cmd -c "import ai_terminal; print('✓ Module import successful')" &> /dev/null; then
                print_success "Module import test passed with $python_cmd"
            else
                print_warning "Module import test failed - this may be normal if dependencies aren't fully installed"
            fi
        fi
    else
        if $python_cmd -c "import ai_terminal; print('✓ Module import successful')" &> /dev/null; then
            print_success "Module import test passed with $python_cmd"
        else
            print_warning "Module import test failed - this may be normal if dependencies aren't fully installed"
        fi
    fi

    # Test 2: CLI entry point functionality
    print_info "Testing CLI entry points..."
    local cli_test_passed=false

    # Test with Poetry
    if [[ -f "pyproject.toml" ]] && command -v poetry &> /dev/null; then
        if poetry run ai-terminal --help &> /dev/null; then
            print_success "CLI entry point test passed with Poetry"
            cli_test_passed=true
        elif poetry run python -m ai_terminal --help &> /dev/null; then
            print_success "CLI module test passed with Poetry"
            cli_test_passed=true
        fi
    fi

    # Test with venv
    if [[ "$cli_test_passed" == "false" ]] && [[ -d "venv" ]]; then
        source venv/bin/activate
        if ai-terminal --help &> /dev/null; then
            print_success "CLI entry point test passed with venv"
            cli_test_passed=true
        elif python -m ai_terminal --help &> /dev/null; then
            print_success "CLI module test passed with venv"
            cli_test_passed=true
        fi
    fi

    # Test with system Python
    if [[ "$cli_test_passed" == "false" ]]; then
        if command -v ai-terminal &> /dev/null && ai-terminal --help &> /dev/null; then
            print_success "CLI entry point test passed with system installation"
            cli_test_passed=true
        elif $python_cmd -m ai_terminal --help &> /dev/null; then
            print_success "CLI module test passed with $python_cmd"
            cli_test_passed=true
        fi
    fi

    if [[ "$cli_test_passed" == "false" ]]; then
        print_warning "CLI tests failed - this may indicate missing dependencies"
        print_info "The installation may still work after running the onboarding wizard"
    fi

    # Test 3: Global commands
    print_info "Testing global commands..."
    local global_test_passed=0
    local global_commands=("ai-terminal" "ai-chat" "ai-quick" "ai-health" "ai-config" "ai-setup")

    for cmd in "${global_commands[@]}"; do
        if command -v "$cmd" &> /dev/null; then
            global_test_passed=$((global_test_passed + 1))
            print_debug "Global command available: $cmd"
        fi
    done

    if [[ $global_test_passed -eq ${#global_commands[@]} ]]; then
        print_success "All global commands are available ($global_test_passed/${#global_commands[@]})"
    elif [[ $global_test_passed -gt 0 ]]; then
        print_warning "Some global commands are available ($global_test_passed/${#global_commands[@]})"
    else
        print_warning "No global commands found - may need shell restart"
    fi

    # Test 4: Configuration directory
    print_info "Testing configuration setup..."
    local config_dirs=(
        "$HOME/.config/ai-terminal"
        "$HOME/.ai-terminal"
        "$INSTALL_DIR/.ai-terminal"
    )

    local config_dir_exists=false
    for dir in "${config_dirs[@]}"; do
        if [[ -d "$dir" ]]; then
            print_debug "Configuration directory found: $dir"
            config_dir_exists=true
            break
        fi
    done

    if [[ "$config_dir_exists" == "true" ]]; then
        print_success "Configuration directory structure is ready"
    else
        print_info "Configuration directory will be created during first run"
    fi

    print_success "Installation testing completed"
    return 0
}

run_setup_wizard() {
    print_step "Launching AI Terminal onboarding wizard..." 10 10

    cd "$INSTALL_DIR"

    # Determine which Python to use
    local python_cmd=$(get_python_command)
    if [[ -z "$python_cmd" ]]; then
        python_cmd="python3"  # Fallback
    fi

    echo -e "${YELLOW}${BOLD}"
    echo "╔══════════════════════════════════════════════════════════════════════════════╗"
    echo "║                          🎯 Onboarding Wizard Starting                      ║"
    echo "║                                                                              ║"
    echo -e "║  The interactive onboarding wizard will guide you through:                ║"
    echo -e "║  ${GREEN}• Selecting your AI provider (OpenAI, Anthropic, Google, Mistral, etc.)${YELLOW}    ║"
    echo -e "║  ${GREEN}• Configuring API keys securely with multiple input methods${YELLOW}             ║"
    echo -e "║  ${GREEN}• Setting preferences (models, temperature, tokens, security)${YELLOW}           ║"
    echo -e "║  ${GREEN}• Configuring advanced features (agents, tools, integrations)${YELLOW}          ║"
    echo -e "║  ${GREEN}• Testing your configuration to ensure everything works${YELLOW}                ║"
    echo "║                                                                              ║"
    echo -e "║  ${PURPLE}💡 Tip: Have your API key ready for easy pasting!${YELLOW}                      ║"
    echo -e "║  ${PURPLE}🔒 All credentials are encrypted and stored securely${YELLOW}                   ║"
    echo "╚══════════════════════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"

    echo -e "${CYAN}Press Enter to start the onboarding wizard, or Ctrl+C to skip...${NC}"
    read -r

    print_info "Starting AI Terminal onboarding wizard..."

    # Run onboarding wizard with multiple fallback methods
    local wizard_success=false

    # Method 1: Poetry
    if [[ -f "pyproject.toml" ]] && command -v poetry &> /dev/null; then
        print_info "Running onboarding with Poetry..."
        if poetry run ai-terminal 2>/dev/null; then
            wizard_success=true
        elif poetry run python -m ai_terminal 2>/dev/null; then
            wizard_success=true
        fi
    fi

    # Method 2: Virtual environment
    if [[ "$wizard_success" == "false" ]] && [[ -d "venv" ]]; then
        print_info "Running onboarding with virtual environment..."
        source venv/bin/activate
        if ai-terminal 2>/dev/null; then
            wizard_success=true
        elif python -m ai_terminal 2>/dev/null; then
            wizard_success=true
        fi
    fi

    # Method 3: System installation
    if [[ "$wizard_success" == "false" ]]; then
        print_info "Running onboarding with system Python..."
        if command -v ai-terminal &> /dev/null && ai-terminal 2>/dev/null; then
            wizard_success=true
        elif $python_cmd -m ai_terminal 2>/dev/null; then
            wizard_success=true
        fi
    fi

    if [[ "$wizard_success" == "true" ]]; then
        print_success "Onboarding wizard completed successfully!"
    else
        print_warning "Onboarding wizard encountered issues"
        print_info "You can run it manually later using: ai-terminal"
        print_info "Or try: $python_cmd -m ai_terminal"
    fi
}

show_completion() {
    local python_cmd=$(get_python_command)
    local python_version=""
    if [[ -n "$python_cmd" ]]; then
        python_version=$($python_cmd --version 2>&1 | grep -oE '[0-9]+\.[0-9]+\.[0-9]+')
    fi

    echo -e "${GREEN}${BOLD}"
    echo "╔══════════════════════════════════════════════════════════════════════════════╗"
    echo "║                     🎉 Installation Complete! 🎉                           ║"
    echo "║                                                                              ║"
    echo -e "║  AI Terminal has been successfully installed and configured in WSL!         ║"
    echo -e "║  🧠 Smart updates enabled - future runs will only update what's needed      ║"
    echo -e "║  🔒 Enterprise-grade security and encryption configured                   ║"
    echo -e "║  🚀 Modern WSL2 features detected and optimized                          ║"
    echo "╚══════════════════════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"

    echo -e "${CYAN}📍 Installation Details:${NC}"
    echo -e "  ${DIM}Location:${NC} $INSTALL_DIR"
    echo -e "  ${DIM}State File:${NC} $CONFIG_FILE"
    echo -e "  ${DIM}Python Version:${NC} $python_version ($python_cmd)"
    echo -e "  ${DIM}System:${NC} ${DISTRO_NAME:-Unknown} (${DISTRO_VERSION:-Unknown})"
    if [[ "${SYSTEMD_AVAILABLE:-false}" == "true" ]]; then
        echo -e "  ${DIM}Systemd:${NC} ✅ Available (v${SYSTEMD_VERSION:-unknown})"
    fi
    if [[ "${GPU_NVIDIA:-false}" == "true" ]]; then
        echo -e "  ${DIM}GPU Support:${NC} ✅ NVIDIA (v${GPU_NVIDIA_VERSION:-unknown})"
    fi
    echo ""

    echo -e "${YELLOW}🚀 Global Commands (Available Anywhere):${NC}"
    echo -e "  ${BLUE}ait${NC}                         - Activate AI Terminal environment"
    echo -e "  ${BLUE}ai-terminal${NC}                 - Start AI Terminal application"
    echo -e "  ${BLUE}ai-chat${NC}                     - Start interactive chat session"
    echo -e "  ${BLUE}ai-quick 'message'${NC}          - Send quick message to AI"
    echo -e "  ${BLUE}ai-health${NC}                   - Check system health and status"
    echo -e "  ${BLUE}ai-config${NC}                   - Show current configuration"
    echo -e "  ${BLUE}ai-setup${NC}                    - Re-run onboarding wizard"
    echo ""

    echo -e "${YELLOW}🔧 Installer Commands:${NC}"
    echo -e "  ${BLUE}./auto_install_wsl.sh --check${NC}       - Check what needs updating"
    echo -e "  ${BLUE}./auto_install_wsl.sh --update${NC}      - Update components only"
    echo -e "  ${BLUE}./auto_install_wsl.sh --setup-only${NC}  - Run onboarding wizard only"
    echo -e "  ${BLUE}./auto_install_wsl.sh --force${NC}       - Force complete reinstall"
    echo -e "  ${BLUE}./auto_install_wsl.sh --help${NC}        - Show all available options"
    echo ""

    echo -e "${YELLOW}📚 Manual Commands:${NC}"
    echo -e "  ${BLUE}cd ~/ai-terminal${NC}            - Navigate to project directory"
    echo -e "  ${BLUE}poetry shell${NC}                - Activate Poetry environment"
    echo -e "  ${BLUE}$python_cmd -m ai_terminal${NC}  - Run using Python module"
    echo -e "  ${BLUE}source ~/ai-terminal/activate.sh${NC} - Activate project environment"
    echo ""

    echo -e "${PURPLE}🎯 Quick Start:${NC}"
    echo -e "  ${GREEN}1.${NC} Run ${BLUE}ai-terminal${NC} to start the application"
    echo -e "  ${GREEN}2.${NC} If not configured, the onboarding wizard will start automatically"
    echo -e "  ${GREEN}3.${NC} Use ${BLUE}ai-chat${NC} for quick access to chat interface"
    echo -e "  ${GREEN}4.${NC} Try ${BLUE}ai-quick 'Hello, AI!'${NC} for instant responses"
    echo ""

    echo -e "${GREEN}✨ Enjoy using AI Terminal! ✨${NC}"
    echo ""
    echo -e "${PURPLE}💡 Tips:${NC}"
    echo -e "  ${DIM}• Restart your terminal or run 'source ~/.bashrc' to use new aliases${NC}"
    echo -e "  ${DIM}• Re-running this script will only update what's changed (smart updates)${NC}"
    echo -e "  ${DIM}• All credentials are encrypted and stored securely${NC}"
    echo -e "  ${DIM}• Use --debug flag with any command for detailed troubleshooting${NC}"
}

# =============================================================================
# Main Installation Process
# =============================================================================

show_help() {
    echo -e "${CYAN}${BOLD}AI Terminal WSL Auto-Installer v$SCRIPT_VERSION${NC}"
    echo -e "${DIM}Comprehensive installation script for AI Terminal in WSL environments${NC}"
    echo ""
    echo -e "${YELLOW}Usage:${NC} $0 [OPTIONS]"
    echo ""
    echo -e "${YELLOW}Options:${NC}"
    echo -e "  ${GREEN}-h, --help${NC}          Show this help message and exit"
    echo -e "  ${GREEN}-f, --force${NC}         Force complete reinstallation (removes all state)"
    echo -e "  ${GREEN}-u, --update${NC}        Update components only (skip onboarding wizard)"
    echo -e "  ${GREEN}-s, --setup-only${NC}    Run onboarding wizard only (requires existing installation)"
    echo -e "  ${GREEN}-c, --check${NC}         Check installation status and what needs updating"
    echo -e "  ${GREEN}-d, --debug${NC}         Enable debug mode with verbose logging"
    echo -e "  ${GREEN}--version${NC}           Show detailed version and compatibility information"
    echo ""
    echo -e "${YELLOW}Examples:${NC}"
    echo -e "  ${BLUE}$0${NC}                     # Smart install/update (recommended)"
    echo -e "  ${BLUE}$0 --check${NC}            # Check what needs updating without making changes"
    echo -e "  ${BLUE}$0 --force${NC}            # Force complete reinstallation from scratch"
    echo -e "  ${BLUE}$0 --update${NC}           # Update components only, skip onboarding"
    echo -e "  ${BLUE}$0 --setup-only${NC}       # Re-run onboarding wizard only"
    echo -e "  ${BLUE}$0 --debug${NC}            # Run with detailed debugging information"
    echo ""
    echo -e "${YELLOW}Features:${NC}"
    echo -e "  ${DIM}• Smart detection and incremental updates${NC}"
    echo -e "  ${DIM}• Support for Python 3.9-3.12 with automatic version selection${NC}"
    echo -e "  ${DIM}• Modern WSL2 features (systemd, GPU support detection)${NC}"
    echo -e "  ${DIM}• Comprehensive dependency management and security validation${NC}"
    echo -e "  ${DIM}• Enterprise-grade error handling and recovery${NC}"
    echo -e "  ${DIM}• Cross-platform compatibility (Ubuntu 20.04+, Debian 11+)${NC}"
    echo ""
    echo -e "${YELLOW}System Requirements:${NC}"
    echo -e "  ${DIM}• WSL2 (Windows Subsystem for Linux 2)${NC}"
    echo -e "  ${DIM}• Ubuntu 20.04+ or Debian 11+ (or compatible distribution)${NC}"
    echo -e "  ${DIM}• Python ${MIN_PYTHON_VERSION}+ (will be installed if missing)${NC}"
    echo -e "  ${DIM}• Sudo access for system package installation${NC}"
    echo -e "  ${DIM}• Internet connection for downloading dependencies${NC}"
    echo ""
    echo -e "${PURPLE}💡 Tip: Run without arguments for the best experience with smart detection!${NC}"
}

check_status() {
    print_header
    print_step "Checking AI Terminal installation status..."

    if detect_existing_installation; then
        echo -e "${YELLOW}Some components need updating${NC}"
        exit 0
    else
        echo -e "${GREEN}All components are up to date!${NC}"
        exit 0
    fi
}



# =============================================================================
# Error Handling and Cleanup
# =============================================================================

# Enhanced error handler with context and recovery suggestions
error_handler() {
    local exit_code=$?
    local line_number=$1
    local bash_lineno=$2
    local last_command=$3
    local funcstack=("${FUNCNAME[@]}")

    print_error "Installation failed at line $line_number (exit code: $exit_code)"
    print_error "Command: $last_command"

    if [[ ${#funcstack[@]} -gt 1 ]]; then
        print_error "Function: ${funcstack[1]}"
    fi

    echo ""
    print_info "Troubleshooting suggestions:"
    echo -e "  ${DIM}• Check your internet connection${NC}"
    echo -e "  ${DIM}• Ensure you have sudo access${NC}"
    echo -e "  ${DIM}• Try running with --debug for more information${NC}"
    echo -e "  ${DIM}• Check WSL2 is properly configured${NC}"
    echo -e "  ${DIM}• Verify Ubuntu/Debian package repositories are accessible${NC}"
    echo ""
    print_info "Recovery options:"
    echo -e "  ${DIM}• Run: $0 --force (complete reinstall)${NC}"
    echo -e "  ${DIM}• Run: $0 --check (check current state)${NC}"
    echo -e "  ${DIM}• Check logs in: /tmp/ai-terminal-system-info${NC}"

    # Cleanup on error
    cleanup_on_error

    exit $exit_code
}

# Cleanup function for error conditions
cleanup_on_error() {
    print_info "Performing cleanup..."

    # Remove any temporary files
    rm -f /tmp/ai-terminal-* 2>/dev/null || true

    # Reset any partial installations that might be in a bad state
    if [[ -d "$INSTALL_DIR" ]] && [[ ! -f "$INSTALL_DIR/pyproject.toml" ]]; then
        print_warning "Removing incomplete installation directory"
        rm -rf "$INSTALL_DIR" 2>/dev/null || true
    fi
}

# Graceful exit handler
graceful_exit() {
    local exit_code=${1:-0}

    if [[ $exit_code -eq 0 ]]; then
        print_success "Script completed successfully"
    else
        print_warning "Script exited with code $exit_code"
    fi

    # Cleanup temporary files
    rm -f /tmp/ai-terminal-system-info 2>/dev/null || true

    exit $exit_code
}

# Set up error trapping
trap 'error_handler ${LINENO} ${BASH_LINENO} "$BASH_COMMAND"' ERR
trap 'graceful_exit 130' INT  # Ctrl+C
trap 'graceful_exit 143' TERM # Termination

# =============================================================================
# Script Execution
# =============================================================================

# Validate environment before starting
validate_environment() {
    # Check if running as root
    if [[ $EUID -eq 0 ]]; then
        print_error "This script should not be run as root"
        print_info "Please run as a regular user with sudo access"
        exit 1
    fi

    # Check if we're in WSL
    if [[ ! -f /proc/version ]] || ! grep -q "microsoft\|WSL" /proc/version 2>/dev/null; then
        print_error "This script must be run in WSL (Windows Subsystem for Linux)"
        print_info "Please install WSL2 and a compatible Linux distribution"
        exit 1
    fi

    # Check basic commands are available
    local required_commands=("curl" "grep" "awk" "sed" "cut" "sort" "uniq")
    local missing_commands=()

    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" >/dev/null 2>&1; then
            missing_commands+=("$cmd")
        fi
    done

    if [[ ${#missing_commands[@]} -gt 0 ]]; then
        print_error "Missing required commands: ${missing_commands[*]}"
        print_info "Please install basic system utilities first"
        exit 1
    fi
}

# Main execution function (replaces the previous main function)
main_installation() {
    # Validate environment first
    validate_environment

    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -f|--force)
                FORCE_REINSTALL=true
                shift
                ;;
            -u|--update)
                UPDATE_ONLY=true
                shift
                ;;
            -s|--setup-only)
                SETUP_ONLY=true
                shift
                ;;
            -c|--check)
                check_status
                ;;
            -d|--debug)
                DEBUG=true
                set -x  # Enable bash debugging
                shift
                ;;
            --version)
                echo "AI Terminal WSL Auto-Installer v$SCRIPT_VERSION"
                echo "Compatible with Python ${MIN_PYTHON_VERSION}+ (Preferred: ${PREFERRED_PYTHON_VERSION})"
                echo "Supports Ubuntu 20.04+, Debian 11+, and other Debian-based distributions"
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done

    print_header

    # Handle setup-only mode
    if [[ "$SETUP_ONLY" == "true" ]]; then
        if [[ ! -d "$INSTALL_DIR" ]]; then
            print_error "AI Terminal is not installed. Run without --setup-only first."
            exit 1
        fi
        run_setup_wizard
        exit 0
    fi

    # Check if update is needed (unless forced)
    if [[ "$FORCE_REINSTALL" != "true" ]]; then
        if ! detect_existing_installation; then
            if [[ "$UPDATE_ONLY" == "true" ]]; then
                print_success "All components are up to date!"
                exit 0
            else
                print_success "All components are up to date!"
                echo ""
                echo -e "${CYAN}Options:${NC}"
                echo -e "  ${BLUE}$0 --setup-only${NC}     - Run onboarding wizard"
                echo -e "  ${BLUE}$0 --force${NC}          - Force reinstallation"
                echo -e "  ${BLUE}ai-terminal${NC}          - Start using AI Terminal"
                exit 0
            fi
        fi
    else
        print_warning "Force reinstallation requested - removing state file"
        rm -f "$CONFIG_FILE"
    fi

    echo "Starting intelligent installation/update..."
    sleep 2

    # Detect system information first
    detect_system_info

    # Core installation steps (smart - only run what's needed)
    check_wsl
    check_sudo
    update_system
    install_dependencies
    install_python
    install_poetry
    copy_project
    install_project_dependencies
    create_shortcuts
    test_installation

    # Interactive setup (skip if update-only)
    if [[ "$UPDATE_ONLY" != "true" ]]; then
        # Check if setup has been run before
        if [[ ! -f "$HOME/.config/ai-terminal/config.yaml" ]] && [[ ! -f "$HOME/.ai-terminal/config.yaml" ]] && [[ ! -f "$INSTALL_DIR/.ai-terminal/config.yaml" ]]; then
            print_info "No existing configuration found. Running onboarding wizard..."
            run_setup_wizard
        else
            print_success "Configuration exists. AI Terminal is ready to use!"
            print_info "Use 'ai-setup' or '$0 --setup-only' to reconfigure."
        fi
    fi

    # Completion
    show_completion

    # Test global commands
    print_step "Testing global commands..."
    if command -v ait >/dev/null 2>&1; then
        print_success "Global commands are working!"
    else
        print_warning "Global commands may need a shell restart"
        print_info "Try opening a new terminal or run: hash -r"
    fi

    # Source bashrc to enable aliases
    echo "Reloading shell configuration..."
    source ~/.bashrc 2>/dev/null || true
    hash -r 2>/dev/null || true  # Refresh command hash table

    echo ""
    echo -e "${GREEN}🎯 Ready to use! Try: ${BLUE}ait${NC} or ${BLUE}ai-terminal${NC}"
    echo -e "${CYAN}💡 If commands aren't found, open a new terminal window${NC}"
}

# Run the script
main_installation "$@"
