"""
Terminal header component for AI Terminal.

Displays status information including current model, session, connection status,
and real-time indicators for AI processing states.
"""

import asyncio
from datetime import datetime
from typing import Optional

from rich.text import Text
from textual.containers import Horizontal
from textual.widgets import Static

from ai_terminal.core.config import Config
from ai_terminal.core.logger import LoggerMixin
from ai_terminal.storage.session import SessionManager


class TerminalHeader(Static, LoggerMixin):
    """
    Terminal header component showing status information.
    
    Displays:
    - Current AI model and provider
    - Session information
    - Connection status
    - Processing indicators
    - Token usage
    """
    
    DEFAULT_CSS = """
    TerminalHeader {
        height: 3;
        background: $primary;
        color: $text;
        border-bottom: solid $accent;
    }
    
    .header-section {
        height: 1;
        padding: 0 1;
    }
    
    .status-line {
        background: $surface;
        color: $text;
    }
    
    .thinking-indicator {
        background: $warning;
        color: $text;
    }
    
    .error-indicator {
        background: $error;
        color: $text;
    }
    
    .success-indicator {
        background: $success;
        color: $text;
    }
    """
    
    def __init__(
        self,
        config: Config,
        session_manager: <PERSON><PERSON>ana<PERSON>,
        **kwargs
    ):
        """Initialize the terminal header."""
        super().__init__(**kwargs)
        
        self.config = config
        self.session_manager = session_manager
        
        # State
        self.is_thinking = False
        self.current_model = config.get("ai.default_model", "Unknown")
        self.current_provider = config.get("ai.default_provider", "Unknown")
        self.session_name = "main"
        self.connection_status = "disconnected"
        self.token_count = 0
        self.status_message = ""
        self.status_type = "info"
        
        # Animation state
        self.thinking_animation_task: Optional[asyncio.Task] = None
        self.thinking_frame = 0
        self.thinking_frames = ["⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"]
    
    def on_mount(self) -> None:
        """Handle component mount."""
        self.update_display()
        self.set_interval(1.0, self.update_time)
    
    def update_display(self) -> None:
        """Update the header display."""
        # Main status line
        status_line = self._build_status_line()
        
        # Thinking indicator line
        thinking_line = self._build_thinking_line()
        
        # Status message line
        message_line = self._build_message_line()
        
        # Combine all lines
        content = Text()
        content.append(status_line)
        content.append("\n")
        content.append(thinking_line)
        content.append("\n")
        content.append(message_line)
        
        self.update(content)
    
    def _build_status_line(self) -> Text:
        """Build the main status line."""
        line = Text()
        
        # AI model info
        line.append("🤖 ", style="bold blue")
        line.append(f"{self.current_provider}/{self.current_model}", style="cyan")
        
        # Session info
        line.append(" | 💬 ", style="bold green")
        line.append(f"Session: {self.session_name}", style="green")
        
        # Connection status
        status_color = "green" if self.connection_status == "connected" else "red"
        status_icon = "🟢" if self.connection_status == "connected" else "🔴"
        line.append(f" | {status_icon} ", style=status_color)
        line.append(self.connection_status.title(), style=status_color)
        
        # Token count
        if self.token_count > 0:
            line.append(" | 🔢 ", style="bold yellow")
            line.append(f"{self.token_count:,} tokens", style="yellow")
        
        # Current time
        current_time = datetime.now().strftime("%H:%M:%S")
        line.append(f" | ⏰ {current_time}", style="dim white")
        
        return line
    
    def _build_thinking_line(self) -> Text:
        """Build the thinking indicator line."""
        line = Text()
        
        if self.is_thinking:
            spinner = self.thinking_frames[self.thinking_frame]
            line.append(f"{spinner} AI is thinking...", style="bold yellow")
        else:
            line.append("Ready for input", style="dim green")
        
        return line
    
    def _build_message_line(self) -> Text:
        """Build the status message line."""
        line = Text()
        
        if self.status_message:
            if self.status_type == "error":
                line.append("❌ ", style="bold red")
                line.append(self.status_message, style="red")
            elif self.status_type == "success":
                line.append("✅ ", style="bold green")
                line.append(self.status_message, style="green")
            elif self.status_type == "warning":
                line.append("⚠️ ", style="bold yellow")
                line.append(self.status_message, style="yellow")
            else:
                line.append("ℹ️ ", style="bold blue")
                line.append(self.status_message, style="blue")
        else:
            line.append("AI Terminal - Autonomous AI Assistant", style="dim white")
        
        return line
    
    async def set_thinking(self, thinking: bool) -> None:
        """Set thinking state and start/stop animation."""
        self.is_thinking = thinking
        
        if thinking and not self.thinking_animation_task:
            self.thinking_animation_task = asyncio.create_task(
                self._animate_thinking()
            )
        elif not thinking and self.thinking_animation_task:
            self.thinking_animation_task.cancel()
            self.thinking_animation_task = None
            self.thinking_frame = 0
        
        self.update_display()
    
    async def _animate_thinking(self) -> None:
        """Animate the thinking indicator."""
        try:
            while self.is_thinking:
                self.thinking_frame = (self.thinking_frame + 1) % len(self.thinking_frames)
                self.update_display()
                await asyncio.sleep(0.1)
        except asyncio.CancelledError:
            pass
    
    async def update_model_info(self, provider: str, model: str) -> None:
        """Update current model information."""
        self.current_provider = provider
        self.current_model = model
        self.update_display()
    
    async def update_session_info(self) -> None:
        """Update session information."""
        try:
            current_session = await self.session_manager.get_current_session()
            if current_session:
                self.session_name = current_session.name
            self.update_display()
        except Exception as e:
            self.logger.error(f"Failed to update session info: {e}")
    
    async def update_connection_status(self, status: str) -> None:
        """Update connection status."""
        self.connection_status = status
        self.update_display()
    
    async def update_token_count(self, count: int) -> None:
        """Update token count."""
        self.token_count = count
        self.update_display()
    
    async def show_status(
        self, 
        message: str, 
        status_type: str = "info",
        duration: float = 3.0
    ) -> None:
        """Show a temporary status message."""
        self.status_message = message
        self.status_type = status_type
        self.update_display()
        
        # Clear message after duration
        if duration > 0:
            await asyncio.sleep(duration)
            self.status_message = ""
            self.update_display()
    
    def update_time(self) -> None:
        """Update the time display (called by timer)."""
        if not self.is_thinking:  # Don't update during thinking animation
            self.update_display()
    
    async def refresh(self) -> None:
        """Refresh all header information."""
        await self.update_session_info()
        self.update_display()
    
    def on_unmount(self) -> None:
        """Handle component unmount."""
        if self.thinking_animation_task:
            self.thinking_animation_task.cancel()
