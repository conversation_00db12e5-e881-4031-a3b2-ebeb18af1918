"""
Terminal chat component for AI Terminal.

Displays conversation history with rich formatting, syntax highlighting,
streaming responses, and tool call visualizations.
"""

import asyncio
import json
from datetime import datetime
from typing import Any, Dict, List, Optional

from rich.console import Console
from rich.markdown import Markdown
from rich.panel import Panel
from rich.syntax import Syntax
from rich.text import Text
from textual import on
from textual.containers import ScrollableContainer, Vertical, Horizontal, Container
from textual.events import Key
from textual.widgets import Static, Button, Label, ProgressBar, Input

from ai_terminal.agents.engine import AgentEngine
from ai_terminal.agents.response import AgentR<PERSON>ponse, StreamingResponse, ToolCall
from ai_terminal.core.config import Config
from ai_terminal.core.logger import LoggerMixin
from ai_terminal.storage.models import Message, Session
from ai_terminal.storage.session import SessionManager


class MessageItem(Static):
    """Individual message display component."""
    
    DEFAULT_CSS = """
    MessageItem {
        width: 100%;
        height: auto;
        margin: 1 0;
        padding: 1;
        border-left: solid $accent;
    }
    
    .user-message {
        border-left: solid $primary;
        background: $surface;
    }
    
    .assistant-message {
        border-left: solid $success;
        background: $panel;
    }
    
    .system-message {
        border-left: solid $warning;
        background: $boost;
    }
    
    .tool-message {
        border-left: solid $error;
        background: $error 20%;
    }
    """
    
    def __init__(self, message: Message, config: Config, **kwargs):
        """Initialize message item."""
        super().__init__(**kwargs)
        self.message = message
        self.config = config
        self.add_class(f"{message.role}-message")
    
    def on_mount(self) -> None:
        """Handle component mount."""
        self.update_content()
    
    def update_content(self) -> None:
        """Update message content display."""
        content = Text()
        
        # Message header with timestamp and role
        timestamp = self.message.timestamp.strftime("%H:%M:%S")
        role_icon = self._get_role_icon(self.message.role)
        
        header = Text()
        header.append(f"{role_icon} ", style="bold")
        header.append(f"{self.message.role.title()}", style="bold cyan")
        header.append(f" • {timestamp}", style="dim white")
        
        if self.message.model:
            header.append(f" • {self.message.model}", style="dim yellow")
        
        content.append(header)
        content.append("\n\n")
        
        # Message content
        if self.message.role == "assistant":
            # Render markdown for assistant messages
            try:
                markdown = Markdown(self.message.content)
                content.append(str(markdown))
            except Exception:
                content.append(self.message.content, style="white")
        else:
            content.append(self.message.content, style="white")
        
        # Tool calls if present
        if self.message.tool_calls:
            content.append("\n\n")
            content.append("🔧 Tool Calls:", style="bold yellow")
            for tool_call in self.message.tool_calls:
                content.append(f"\n  • {tool_call.get('name', 'Unknown')}", style="cyan")
                if tool_call.get('args'):
                    content.append(f" with {len(tool_call['args'])} arguments", style="dim white")
        
        self.update(content)
    
    def _get_role_icon(self, role: str) -> str:
        """Get icon for message role."""
        icons = {
            "user": "👤",
            "assistant": "🤖", 
            "system": "⚙️",
            "tool": "🔧",
        }
        return icons.get(role, "💬")


class StreamingMessage(Static):
    """Component for displaying streaming assistant responses."""
    
    DEFAULT_CSS = """
    StreamingMessage {
        width: 100%;
        height: auto;
        margin: 1 0;
        padding: 1;
        border-left: solid $success;
        background: $panel;
    }
    
    .streaming-cursor {
        background: $success;
        color: $text;
    }
    """
    
    def __init__(self, **kwargs):
        """Initialize streaming message."""
        super().__init__(**kwargs)
        self.content_buffer = ""
        self.is_streaming = True
        self.cursor_visible = True
        self.cursor_task: Optional[asyncio.Task] = None
    
    def on_mount(self) -> None:
        """Handle component mount."""
        self.cursor_task = asyncio.create_task(self._animate_cursor())
        self.update_display()
    
    async def _animate_cursor(self) -> None:
        """Animate the streaming cursor."""
        try:
            while self.is_streaming:
                self.cursor_visible = not self.cursor_visible
                self.update_display()
                await asyncio.sleep(0.5)
        except asyncio.CancelledError:
            pass
    
    def update_display(self) -> None:
        """Update the streaming display."""
        content = Text()
        
        # Header
        header = Text()
        header.append("🤖 ", style="bold")
        header.append("Assistant", style="bold cyan")
        header.append(" • Streaming...", style="dim yellow")
        
        content.append(header)
        content.append("\n\n")
        
        # Content with cursor
        content.append(self.content_buffer, style="white")
        
        if self.is_streaming and self.cursor_visible:
            content.append("▋", style="streaming-cursor")
        
        self.update(content)
    
    async def append_content(self, text: str) -> None:
        """Append text to the streaming content."""
        self.content_buffer += text
        self.update_display()
    
    async def finish_streaming(self) -> str:
        """Finish streaming and return final content."""
        self.is_streaming = False
        if self.cursor_task:
            self.cursor_task.cancel()
        self.update_display()
        return self.content_buffer
    
    def on_unmount(self) -> None:
        """Handle component unmount."""
        if self.cursor_task:
            self.cursor_task.cancel()


class ToolCallDisplay(Static):
    """Component for displaying tool calls."""
    
    DEFAULT_CSS = """
    ToolCallDisplay {
        width: 100%;
        height: auto;
        margin: 1 0;
        padding: 1;
        border-left: solid $error;
        background: $error 20%;
    }
    """
    
    def __init__(self, tool_name: str, args: dict, **kwargs):
        """Initialize tool call display."""
        super().__init__(**kwargs)
        self.tool_name = tool_name
        self.args = args
    
    def on_mount(self) -> None:
        """Handle component mount."""
        self.update_content()
    
    def update_content(self) -> None:
        """Update tool call display."""
        content = Text()
        
        # Header
        header = Text()
        header.append("🔧 ", style="bold")
        header.append("Tool Call", style="bold red")
        header.append(f" • {datetime.now().strftime('%H:%M:%S')}", style="dim white")
        
        content.append(header)
        content.append("\n\n")
        
        # Tool name
        content.append(f"Tool: {self.tool_name}", style="bold cyan")
        
        # Arguments
        if self.args:
            content.append("\nArguments:", style="bold white")
            for key, value in self.args.items():
                content.append(f"\n  {key}: ", style="yellow")
                content.append(str(value), style="white")
        
        self.update(content)


class TerminalChat(ScrollableContainer, LoggerMixin):
    """
    Main chat display component.
    
    Handles conversation display, streaming responses, tool calls,
    and message history with rich formatting and syntax highlighting.
    """
    
    DEFAULT_CSS = """
    TerminalChat {
        background: $background;
        border: solid $primary;
        border-title-align: left;
    }
    """
    
    def __init__(
        self,
        config: Config,
        agent_engine: AgentEngine,
        session_manager: SessionManager,
        **kwargs
    ):
        """Initialize the chat component."""
        super().__init__(**kwargs)
        
        self.config = config
        self.agent_engine = agent_engine
        self.session_manager = session_manager
        
        # State
        self.messages: List[MessageItem] = []
        self.current_streaming: Optional[StreamingMessage] = None
        self.auto_scroll = config.get("ui.auto_scroll", True)
        
        self.border_title = "💬 Conversation"
    
    async def load_session(self, session: Session) -> None:
        """Load messages from a session."""
        try:
            # Clear current messages
            await self.clear()
            
            # Load messages from session
            messages = await self.session_manager.get_session_messages(session.id)
            
            for message in messages:
                await self.add_message(message)
            
            self.border_title = f"💬 {session.name}"
            
        except Exception as e:
            self.logger.error(f"Failed to load session: {e}")
    
    async def add_message(self, message: Message) -> None:
        """Add a message to the chat display."""
        try:
            message_item = MessageItem(message, self.config)
            await self.mount(message_item)
            self.messages.append(message_item)
            
            if self.auto_scroll:
                self.scroll_end()
                
        except Exception as e:
            self.logger.error(f"Failed to add message: {e}")

    async def add_system_message(self, content: str) -> None:
        """Add a system message to the chat display."""
        try:
            from ai_terminal.storage.models import Message
            import uuid
            from datetime import datetime

            system_message = Message(
                id=str(uuid.uuid4()),
                session_id="system",
                role="system",
                content=content,
                timestamp=datetime.now()
            )

            await self.add_message(system_message)

        except Exception as e:
            self.logger.error(f"Failed to add system message: {e}")

    async def start_streaming_response(self) -> StreamingMessage:
        """Start a streaming assistant response."""
        if self.current_streaming:
            await self.current_streaming.remove()
        
        self.current_streaming = StreamingMessage()
        await self.mount(self.current_streaming)
        
        if self.auto_scroll:
            self.scroll_end()
        
        return self.current_streaming
    
    async def finish_streaming_response(self, message: Message) -> None:
        """Finish streaming and convert to regular message."""
        if self.current_streaming:
            await self.current_streaming.remove()
            self.current_streaming = None
        
        await self.add_message(message)
    
    async def show_tool_call(self, tool_name: str, args: dict) -> None:
        """Show a tool call in the chat."""
        try:
            tool_display = ToolCallDisplay(tool_name, args)
            await self.mount(tool_display)
            
            if self.auto_scroll:
                self.scroll_end()
                
        except Exception as e:
            self.logger.error(f"Failed to show tool call: {e}")
    
    async def clear(self) -> None:
        """Clear all messages from the chat."""
        try:
            # Remove all message components
            for message_item in self.messages:
                if message_item.parent:
                    await message_item.remove()
            
            if self.current_streaming and self.current_streaming.parent:
                await self.current_streaming.remove()
                self.current_streaming = None
            
            self.messages.clear()
            
        except Exception as e:
            self.logger.error(f"Failed to clear chat: {e}")
    
    async def refresh(self) -> None:
        """Refresh the chat display."""
        # Refresh all message items
        for message_item in self.messages:
            message_item.update_content()
    
    async def cleanup(self) -> None:
        """Cleanup chat resources."""
        if self.current_streaming:
            await self.current_streaming.finish_streaming()

        self.logger.debug("Chat component cleaned up")


class TerminalChatResponseItem(Static, LoggerMixin):
    """
    Individual response item component for AI responses.

    Displays AI responses with rich formatting, syntax highlighting,
    and interactive elements for code blocks and tool outputs.
    """

    DEFAULT_CSS = """
    TerminalChatResponseItem {
        width: 100%;
        height: auto;
        margin: 1 0;
        padding: 1;
        border-left: solid $success;
        background: $panel;
    }

    .response-header {
        height: 1;
        background: $success;
        color: $text;
        padding: 0 1;
    }

    .response-content {
        padding: 1;
        background: $surface;
    }

    .code-block {
        background: $boost;
        border: solid $accent;
        padding: 1;
        margin: 1 0;
    }

    .tool-output {
        background: $warning 20%;
        border: solid $warning;
        padding: 1;
        margin: 1 0;
    }
    """

    def __init__(self, response: AgentResponse, config: Config, **kwargs):
        """Initialize response item."""
        super().__init__(**kwargs)
        self.response = response
        self.config = config
        self.is_streaming = False
        self.content_buffer = ""

    def compose(self):
        """Compose response item layout."""
        with Vertical():
            # Response header
            yield Static(
                f"🤖 Assistant • {datetime.now().strftime('%H:%M:%S')}",
                classes="response-header"
            )

            # Response content
            with Container(classes="response-content"):
                yield Static("", id="response-text")

    def on_mount(self) -> None:
        """Handle component mount."""
        self.update_content()

    def update_content(self) -> None:
        """Update response content display."""
        try:
            content_widget = self.query_one("#response-text", Static)

            if self.response.content:
                # Render markdown content
                try:
                    markdown = Markdown(self.response.content)
                    content_widget.update(markdown)
                except Exception:
                    content_widget.update(self.response.content)
            else:
                content_widget.update("Processing...")

        except Exception as e:
            self.logger.error(f"Failed to update response content: {e}")

    async def stream_content(self, content_chunk: str) -> None:
        """Stream content to the response item."""
        self.content_buffer += content_chunk
        self.is_streaming = True

        try:
            content_widget = self.query_one("#response-text", Static)

            # Update with streaming content and cursor
            display_content = self.content_buffer
            if self.is_streaming:
                display_content += "▋"

            content_widget.update(display_content)

        except Exception as e:
            self.logger.error(f"Failed to stream content: {e}")

    async def finish_streaming(self) -> None:
        """Finish streaming and finalize content."""
        self.is_streaming = False
        self.response.content = self.content_buffer
        self.update_content()


class TerminalChatToolCallCommand(Container, LoggerMixin):
    """
    Tool call command display component.

    Shows tool execution with progress, results, and interactive elements
    for approving or reviewing tool calls before execution.
    """

    DEFAULT_CSS = """
    TerminalChatToolCallCommand {
        width: 100%;
        height: auto;
        margin: 1 0;
        border: solid $error;
        background: $error 10%;
    }

    .tool-header {
        height: 3;
        background: $error;
        color: $text;
        padding: 1;
    }

    .tool-content {
        padding: 1;
        background: $surface;
    }

    .tool-args {
        background: $boost;
        border: solid $accent;
        padding: 1;
        margin: 1 0;
    }

    .tool-result {
        background: $success 20%;
        border: solid $success;
        padding: 1;
        margin: 1 0;
    }

    .tool-error {
        background: $error 20%;
        border: solid $error;
        padding: 1;
        margin: 1 0;
    }

    .tool-progress {
        height: 1;
        margin: 1 0;
    }
    """

    def __init__(
        self,
        tool_call: ToolCall,
        config: Config,
        on_approve: Optional[callable] = None,
        on_deny: Optional[callable] = None,
        **kwargs
    ):
        """Initialize tool call command display."""
        super().__init__(**kwargs)
        self.tool_call = tool_call
        self.config = config
        self.on_approve = on_approve
        self.on_deny = on_deny

        self.status = "pending"  # pending, executing, completed, error
        self.result = None
        self.error = None

    def compose(self):
        """Compose tool call layout."""
        with Vertical():
            # Tool header
            with Container(classes="tool-header"):
                yield Label(f"🔧 Tool Call: {self.tool_call.name}")
                yield Label(f"ID: {self.tool_call.id}")

            # Tool content
            with Container(classes="tool-content"):
                # Tool arguments
                if self.tool_call.args:
                    yield Label("Arguments:", classes="tool-section-header")
                    with Container(classes="tool-args"):
                        args_text = json.dumps(self.tool_call.args, indent=2)
                        yield Static(Syntax(args_text, "json", theme="monokai"))

                # Progress bar
                yield ProgressBar(id="tool-progress", classes="tool-progress")

                # Approval buttons (if needed)
                if self.config.get("security.require_approval", True):
                    with Horizontal():
                        yield Button("Approve", id="approve-btn", variant="success")
                        yield Button("Deny", id="deny-btn", variant="error")

                # Result area
                yield Static("", id="tool-result")

    @on(Button.Pressed, "#approve-btn")
    async def on_approve_button(self, event: Button.Pressed) -> None:
        """Handle approve button press."""
        if self.on_approve:
            await self.on_approve(self.tool_call)
        await self.execute_tool()

    @on(Button.Pressed, "#deny-btn")
    async def on_deny_button(self, event: Button.Pressed) -> None:
        """Handle deny button press."""
        if self.on_deny:
            await self.on_deny(self.tool_call)
        self.status = "denied"
        self.update_result("Tool execution denied by user", is_error=True)

    async def execute_tool(self) -> None:
        """Execute the tool call."""
        try:
            self.status = "executing"
            progress_bar = self.query_one("#tool-progress", ProgressBar)
            progress_bar.update(progress=50)

            # Here you would integrate with the actual tool execution
            # For now, simulate execution
            await asyncio.sleep(1)

            self.status = "completed"
            progress_bar.update(progress=100)
            self.update_result("Tool executed successfully")

        except Exception as e:
            self.status = "error"
            self.error = str(e)
            self.update_result(f"Tool execution failed: {e}", is_error=True)

    def update_result(self, result: str, is_error: bool = False) -> None:
        """Update tool result display."""
        try:
            result_widget = self.query_one("#tool-result", Static)

            if is_error:
                result_widget.add_class("tool-error")
                result_widget.update(f"❌ {result}")
            else:
                result_widget.add_class("tool-result")
                result_widget.update(f"✅ {result}")

        except Exception as e:
            self.logger.error(f"Failed to update tool result: {e}")


class MessageHistory(ScrollableContainer, LoggerMixin):
    """
    Message history component with search and filtering capabilities.

    Provides a searchable, filterable view of conversation history
    with support for exporting and importing conversations.
    """

    DEFAULT_CSS = """
    MessageHistory {
        background: $background;
        border: solid $primary;
        border-title-align: left;
    }

    .history-header {
        height: 3;
        background: $primary;
        color: $text;
        padding: 1;
    }

    .search-bar {
        height: 3;
        background: $surface;
        padding: 1;
    }

    .history-content {
        height: 1fr;
        padding: 1;
    }

    .history-item {
        height: auto;
        margin: 1 0;
        padding: 1;
        border: solid $accent;
        background: $panel;
    }

    .history-item:hover {
        background: $boost;
    }

    .history-item-selected {
        background: $primary;
        color: $text;
    }
    """

    def __init__(
        self,
        session_manager: SessionManager,
        config: Config,
        **kwargs
    ):
        """Initialize message history."""
        super().__init__(**kwargs)
        self.session_manager = session_manager
        self.config = config

        self.messages: List[Message] = []
        self.filtered_messages: List[Message] = []
        self.search_query = ""
        self.selected_message: Optional[Message] = None

        self.border_title = "📜 Message History"

    def compose(self):
        """Compose history layout."""
        with Vertical():
            # Header
            with Container(classes="history-header"):
                yield Label("Message History")
                yield Label(f"Total: {len(self.messages)} messages")

            # Search bar
            with Container(classes="search-bar"):
                yield Input(
                    placeholder="Search messages...",
                    id="search-input"
                )

            # History content
            with Container(classes="history-content", id="history-list"):
                pass

    async def load_history(self, session_id: Optional[str] = None) -> None:
        """Load message history."""
        try:
            if session_id:
                self.messages = await self.session_manager.get_session_messages(session_id)
            else:
                # Load all messages from current session
                current_session = await self.session_manager.get_current_session()
                if current_session:
                    self.messages = await self.session_manager.get_session_messages(current_session.id)

            self.filtered_messages = self.messages.copy()
            await self.update_display()

        except Exception as e:
            self.logger.error(f"Failed to load history: {e}")

    @on(Input.Changed, "#search-input")
    async def on_search_changed(self, event: Input.Changed) -> None:
        """Handle search input changes."""
        self.search_query = event.value.lower()
        await self.filter_messages()

    async def filter_messages(self) -> None:
        """Filter messages based on search query."""
        if not self.search_query:
            self.filtered_messages = self.messages.copy()
        else:
            self.filtered_messages = [
                msg for msg in self.messages
                if self.search_query in msg.content.lower()
                or self.search_query in msg.role.lower()
            ]

        await self.update_display()

    async def update_display(self) -> None:
        """Update history display."""
        try:
            history_list = self.query_one("#history-list", Container)

            # Clear existing items
            await history_list.remove_children()

            # Add filtered messages
            for message in self.filtered_messages[-50:]:  # Show last 50 messages
                history_item = self.create_history_item(message)
                await history_list.mount(history_item)

        except Exception as e:
            self.logger.error(f"Failed to update history display: {e}")

    def create_history_item(self, message: Message) -> Static:
        """Create a history item widget."""
        content = Text()

        # Message header
        timestamp = message.timestamp.strftime("%Y-%m-%d %H:%M:%S")
        role_icon = self._get_role_icon(message.role)

        content.append(f"{role_icon} {message.role.title()}", style="bold cyan")
        content.append(f" • {timestamp}", style="dim white")
        content.append("\n")

        # Message preview (first 100 chars)
        preview = message.content[:100]
        if len(message.content) > 100:
            preview += "..."
        content.append(preview, style="white")

        item = Static(content, classes="history-item")
        item.message = message  # Store reference
        return item

    def _get_role_icon(self, role: str) -> str:
        """Get icon for message role."""
        icons = {
            "user": "👤",
            "assistant": "🤖",
            "system": "⚙️",
            "tool": "🔧",
        }
        return icons.get(role, "💬")
